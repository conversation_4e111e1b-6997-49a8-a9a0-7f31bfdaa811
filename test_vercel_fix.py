#!/usr/bin/env python3
"""
测试 Vercel 部署修复
验证应用在 serverless 环境中能正常启动
"""

import os
import sys
import tempfile
from unittest.mock import patch

def test_app_creation_in_serverless():
    """测试在 serverless 环境中创建应用"""
    print("测试 serverless 环境中的应用创建...")
    
    # 模拟 Vercel 环境
    with patch.dict(os.environ, {'VERCEL': '1', 'USE_SUPABASE_STORAGE': 'true'}):
        try:
            from app import create_app
            app = create_app()
            
            print("✅ 应用在 serverless 环境中成功创建")
            
            # 测试应用配置
            with app.app_context():
                print(f"✅ UPLOAD_FOLDER: {app.config['UPLOAD_FOLDER']}")
                print(f"✅ SECRET_KEY 已设置: {'SECRET_KEY' in app.config}")
                
                # 测试存储服务
                from app.services.storage_service import storage_service
                print(f"✅ 使用 Supabase Storage: {storage_service.use_supabase_storage}")
                
            return True
            
        except Exception as e:
            print(f"❌ 应用创建失败: {e}")
            return False

def test_app_creation_in_local():
    """测试在本地环境中创建应用"""
    print("\n测试本地环境中的应用创建...")
    
    # 模拟本地环境
    env_vars = {'VERCEL': '', 'USE_SUPABASE_STORAGE': ''}
    with patch.dict(os.environ, env_vars, clear=False):
        # 确保移除 VERCEL 环境变量
        if 'VERCEL' in os.environ:
            del os.environ['VERCEL']
        if 'USE_SUPABASE_STORAGE' in os.environ:
            del os.environ['USE_SUPABASE_STORAGE']
            
        try:
            from app import create_app
            app = create_app()
            
            print("✅ 应用在本地环境中成功创建")
            
            # 测试应用配置
            with app.app_context():
                print(f"✅ UPLOAD_FOLDER: {app.config['UPLOAD_FOLDER']}")
                
                # 测试存储服务
                from app.services.storage_service import storage_service
                print(f"✅ 使用本地存储: {not storage_service.use_supabase_storage}")
                
            return True
            
        except Exception as e:
            print(f"❌ 应用创建失败: {e}")
            return False

def test_storage_service():
    """测试存储服务"""
    print("\n测试存储服务...")
    
    try:
        from app.services.storage_service import StorageService
        
        # 测试 serverless 环境
        with patch.dict(os.environ, {'VERCEL': '1'}):
            service = StorageService()
            print(f"✅ Serverless 环境检测: {service.use_supabase_storage}")
        
        # 测试本地环境
        with patch.dict(os.environ, {}, clear=True):
            service = StorageService()
            print(f"✅ 本地环境检测: {not service.use_supabase_storage}")
            
        return True
        
    except Exception as e:
        print(f"❌ 存储服务测试失败: {e}")
        return False

def test_file_handler():
    """测试文件处理器"""
    print("\n测试文件处理器...")
    
    try:
        # 模拟 Flask 应用上下文
        with patch.dict(os.environ, {'VERCEL': '1'}):
            from app import create_app
            app = create_app()
            
            with app.app_context():
                from app.utils.file_handler import ensure_upload_directories
                
                # 在 serverless 环境中应该跳过目录创建
                result = ensure_upload_directories()
                print(f"✅ Serverless 环境中跳过目录创建: {result}")
                
        return True
        
    except Exception as e:
        print(f"❌ 文件处理器测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("=" * 60)
    print("🧪 Vercel 部署修复测试")
    print("=" * 60)
    
    tests = [
        test_app_creation_in_serverless,
        test_app_creation_in_local,
        test_storage_service,
        test_file_handler
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！应用已准备好部署到 Vercel")
        return 0
    else:
        print("⚠️  部分测试失败，请检查错误信息")
        return 1

if __name__ == '__main__':
    sys.exit(main())
